import request from '../index';
import type { SystemLog, PageParams, PageResult } from '@/types';

// 获取日志列表
export const getLogList = (params: PageParams) => {
  return request.get<PageResult<SystemLog>>('/log/list', { params });
};

// 获取所有日志（不分页）
export const getAllLogs = () => {
  return request.get<SystemLog[]>('/log/list');
};

// 获取日志详情
export const getLogDetail = (id: number) => {
  return request.get<SystemLog>(`/log/get/${id}`);
};

// 添加日志
export const addLog = (data: SystemLog) => {
  return request.post<SystemLog>('/log/add', data);
};

// 删除日志
export const deleteLog = (id: number) => {
  return request.delete(`/log/delete/${id}`);
};

// 批量删除日志
export const batchDeleteLog = (ids: number[]) => {
  return request.delete('/log/batch-delete', { data: { ids } });
};
